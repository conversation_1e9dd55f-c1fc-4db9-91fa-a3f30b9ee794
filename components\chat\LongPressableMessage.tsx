import React, { useRef } from 'react';
import { View, Animated } from 'react-native';
import { LongPressGestureHandler, State } from 'react-native-gesture-handler';

interface LongPressableMessageProps {
  children: React.ReactNode;
  onLongPress: (event: any) => void;
  enabled?: boolean;
}

const LongPressableMessage: React.FC<LongPressableMessageProps> = ({
  children,
  onLongPress,
  enabled = true,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const onHandlerStateChange = (event: any) => {
    if (!enabled) return;

    const { state } = event.nativeEvent;

    switch (state) {
      case State.BEGAN:
        // Start scale animation when long press begins
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }).start();
        break;

      case State.ACTIVE:
        // Long press activated - trigger the callback
        onLongPress(event);
        break;

      case State.END:
      case State.CANCELLED:
      case State.FAILED:
        // Reset scale when long press ends
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }).start();
        break;
    }
  };

  if (!enabled) {
    return <View>{children}</View>;
  }

  return (
    <LongPressGestureHandler
      onHandlerStateChange={onHandlerStateChange}
      minDurationMs={500} // 500ms for long press
    >
      <Animated.View
        style={{
          transform: [{ scale: scaleAnim }],
        }}
      >
        {children}
      </Animated.View>
    </LongPressGestureHandler>
  );
};

export default LongPressableMessage;
