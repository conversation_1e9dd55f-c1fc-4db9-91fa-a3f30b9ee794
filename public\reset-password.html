<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Klicktape</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #000000, #1a1a1a, #2a2a2a);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 400px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #FFD700;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ff6b6b;
            margin-top: 20px;
        }
        .manual-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: #FFD700;
            color: #000;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">Klicktape</div>
        <div class="message">
            Redirecting to the app to reset your password...
        </div>
        <div class="spinner"></div>
        <div id="error-message" class="error" style="display: none;">
            Unable to automatically redirect to the app.
            <br><br>
            <a href="#" id="manual-link" class="manual-link">Open Klicktape App</a>
        </div>
    </div>

    <script>
        function getUrlParams() {
            const params = new URLSearchParams(window.location.hash.substring(1));
            return {
                access_token: params.get('access_token'),
                refresh_token: params.get('refresh_token'),
                error: params.get('error'),
                error_description: params.get('error_description')
            };
        }

        function redirectToApp() {
            const params = getUrlParams();
            
            // Check for errors first
            if (params.error) {
                console.error('Auth error:', params.error, params.error_description);
                document.getElementById('error-message').style.display = 'block';
                document.querySelector('.spinner').style.display = 'none';
                document.querySelector('.message').textContent = 'Password reset link has expired or is invalid.';
                return;
            }

            // If we have an access token, redirect to the app
            if (params.access_token) {
                const appUrl = `klicktape://reset-password?access_token=${params.access_token}&refresh_token=${params.refresh_token || ''}`;
                
                // Try to open the app
                window.location.href = appUrl;
                
                // Set up manual link as fallback
                document.getElementById('manual-link').href = appUrl;
                
                // Show manual option after 3 seconds if auto-redirect fails
                setTimeout(() => {
                    document.getElementById('error-message').style.display = 'block';
                    document.querySelector('.spinner').style.display = 'none';
                    document.querySelector('.message').textContent = 'If the app didn\'t open automatically, click below:';
                }, 3000);
            } else {
                // No token found
                document.getElementById('error-message').style.display = 'block';
                document.querySelector('.spinner').style.display = 'none';
                document.querySelector('.message').textContent = 'Invalid password reset link.';
            }
        }

        // Start the redirect process when page loads
        window.onload = redirectToApp;
    </script>
</body>
</html>
